'use client';

import React, { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Textarea } from '@/components/ui/textarea';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { GdprScanResult, ManualReviewItem, ManualReviewSummary } from '@/types/gdpr';
import { Clock, AlertTriangle, CheckCircle, FileText, Scale, Users, Globe, Shield, Eye } from 'lucide-react';

interface ManualReviewDashboardProps {
  recentScans: GdprScanResult[];
}

export function ManualReviewDashboard({ recentScans }: ManualReviewDashboardProps) {
  const [selectedScan, setSelectedScan] = useState<string | null>(null);
  const [reviewNotes, setReviewNotes] = useState<Record<string, string>>({});
  const [reviewAssessments, setReviewAssessments] = useState<Record<string, string>>({});

  // Extract manual review items from all scans
  const getAllManualReviewItems = (): ManualReviewItem[] => {
    const items: ManualReviewItem[] = [];
    
    recentScans.forEach(scan => {
      scan.checks
        .filter(check => check.manualReviewRequired)
        .forEach(check => {
          items.push({
            ruleId: check.ruleId,
            ruleName: check.ruleName,
            category: check.category,
            automatedFindings: check.evidence,
            reviewStatus: 'pending', // Default status
            scanId: scan.scanId,
            targetUrl: scan.targetUrl,
            scanDate: scan.timestamp,
          } as ManualReviewItem & { scanId: string; targetUrl: string; scanDate: string });
        });
    });
    
    return items;
  };

  const manualReviewItems = getAllManualReviewItems();

  // Calculate summary statistics
  const getManualReviewSummary = (): ManualReviewSummary => {
    const totalItems = manualReviewItems.length;
    const pendingReview = manualReviewItems.filter(item => item.reviewStatus === 'pending').length;
    const inProgress = manualReviewItems.filter(item => item.reviewStatus === 'in_progress').length;
    const completed = manualReviewItems.filter(item => item.reviewStatus === 'completed').length;
    const complianceRate = totalItems > 0 ? (completed / totalItems) * 100 : 0;

    return {
      totalItems,
      pendingReview,
      inProgress,
      completed,
      complianceRate,
    };
  };

  const summary = getManualReviewSummary();

  const handleReviewUpdate = (itemKey: string, field: 'notes' | 'assessment', value: string) => {
    if (field === 'notes') {
      setReviewNotes(prev => ({ ...prev, [itemKey]: value }));
    } else {
      setReviewAssessments(prev => ({ ...prev, [itemKey]: value }));
    }
  };

  const getManualReviewIcon = (ruleId: string) => {
    switch (ruleId) {
      case 'GDPR-013': return <Users className="h-4 w-4" />; // Special Category Data
      case 'GDPR-014': return <Shield className="h-4 w-4" />; // Children's Consent
      case 'GDPR-016': return <Globe className="h-4 w-4" />; // International Transfers
      case 'GDPR-018': return <Scale className="h-4 w-4" />; // DPIA
      case 'GDPR-003': return <FileText className="h-4 w-4" />; // Privacy Notice Content
      default: return <Eye className="h-4 w-4" />;
    }
  };

  const getManualReviewDescription = (ruleId: string) => {
    switch (ruleId) {
      case 'GDPR-013':
        return 'Review automated detection of special category data processing and verify consent mechanisms meet legal requirements.';
      case 'GDPR-014':
        return 'Assess age verification mechanisms and parental consent procedures for compliance with children\'s data protection.';
      case 'GDPR-016':
        return 'Evaluate international data transfer safeguards and adequacy decisions for legal compliance.';
      case 'GDPR-018':
        return 'Review Data Protection Impact Assessment documentation and high-risk processing activities.';
      case 'GDPR-003':
        return 'Assess privacy notice content for legal adequacy and completeness of required information.';
      default:
        return 'Manual legal review required to determine compliance with GDPR requirements.';
    }
  };

  if (manualReviewItems.length === 0) {
    return (
      <Card>
        <CardContent className="text-center py-12">
          <CheckCircle className="h-16 w-16 text-green-500 mx-auto mb-4" />
          <h3 className="text-lg font-semibold mb-2">No Manual Reviews Required</h3>
          <p className="text-muted-foreground">
            All recent GDPR scans have been automatically assessed. No items require manual legal review.
          </p>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="space-y-6">
      {/* Summary Statistics */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Items</CardTitle>
            <FileText className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{summary.totalItems}</div>
            <p className="text-xs text-muted-foreground">
              Requiring manual review
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Pending</CardTitle>
            <Clock className="h-4 w-4 text-yellow-500" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-yellow-600">{summary.pendingReview}</div>
            <p className="text-xs text-muted-foreground">
              Awaiting legal review
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">In Progress</CardTitle>
            <AlertTriangle className="h-4 w-4 text-orange-500" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-orange-600">{summary.inProgress}</div>
            <p className="text-xs text-muted-foreground">
              Under review
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Completed</CardTitle>
            <CheckCircle className="h-4 w-4 text-green-500" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-green-600">{summary.completed}</div>
            <p className="text-xs text-muted-foreground">
              Review complete
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Progress Overview */}
      <Card>
        <CardHeader>
          <CardTitle>Review Progress</CardTitle>
          <CardDescription>
            Overall progress of manual legal reviews
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-2">
            <div className="flex justify-between text-sm">
              <span>Completion Rate</span>
              <span>{Math.round(summary.complianceRate)}%</span>
            </div>
            <Progress value={summary.complianceRate} className="w-full" />
            <p className="text-xs text-muted-foreground">
              {summary.completed} of {summary.totalItems} items reviewed
            </p>
          </div>
        </CardContent>
      </Card>

      {/* Manual Review Items */}
      <Card>
        <CardHeader>
          <CardTitle>Manual Review Items</CardTitle>
          <CardDescription>
            Items requiring human legal expertise assessment
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-6">
            {manualReviewItems.map((item, index) => {
              const itemKey = `${(item as any).scanId}-${item.ruleId}`;
              return (
                <div key={itemKey} className="border rounded-lg p-6">
                  <div className="flex items-start justify-between mb-4">
                    <div className="flex items-center gap-3">
                      {getManualReviewIcon(item.ruleId)}
                      <div>
                        <h4 className="font-semibold">{item.ruleName}</h4>
                        <p className="text-sm text-muted-foreground">
                          {item.ruleId} • {(item as any).targetUrl}
                        </p>
                      </div>
                    </div>
                    <Badge variant="outline">
                      <Clock className="h-3 w-3 mr-1" />
                      {item.reviewStatus.replace('_', ' ')}
                    </Badge>
                  </div>

                  <div className="space-y-4">
                    <div>
                      <h5 className="text-sm font-medium mb-2">Legal Review Required:</h5>
                      <p className="text-sm text-muted-foreground">
                        {getManualReviewDescription(item.ruleId)}
                      </p>
                    </div>

                    <div>
                      <h5 className="text-sm font-medium mb-2">Automated Findings:</h5>
                      <div className="space-y-2">
                        {item.automatedFindings.map((finding, findingIndex) => (
                          <div key={findingIndex} className="bg-muted p-3 rounded text-sm">
                            <strong>{finding.type}:</strong> {finding.description}
                            {finding.location && (
                              <div className="text-xs text-muted-foreground mt-1">
                                Location: {finding.location}
                              </div>
                            )}
                          </div>
                        ))}
                      </div>
                    </div>

                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div className="space-y-2">
                        <Label htmlFor={`assessment-${itemKey}`}>Legal Assessment</Label>
                        <Select
                          value={reviewAssessments[itemKey] || ''}
                          onValueChange={(value) => handleReviewUpdate(itemKey, 'assessment', value)}
                        >
                          <SelectTrigger>
                            <SelectValue placeholder="Select assessment" />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="compliant">Compliant</SelectItem>
                            <SelectItem value="non_compliant">Non-Compliant</SelectItem>
                            <SelectItem value="needs_improvement">Needs Improvement</SelectItem>
                          </SelectContent>
                        </Select>
                      </div>

                      <div className="space-y-2">
                        <Label htmlFor={`notes-${itemKey}`}>Review Notes</Label>
                        <Textarea
                          id={`notes-${itemKey}`}
                          placeholder="Enter legal review notes..."
                          value={reviewNotes[itemKey] || ''}
                          onChange={(e) => handleReviewUpdate(itemKey, 'notes', e.target.value)}
                          rows={3}
                        />
                      </div>
                    </div>

                    <div className="flex justify-end">
                      <Button 
                        variant="outline" 
                        size="sm"
                        disabled={!reviewAssessments[itemKey]}
                      >
                        Save Review
                      </Button>
                    </div>
                  </div>
                </div>
              );
            })}
          </div>
        </CardContent>
      </Card>

      {/* Legal Guidance */}
      <Alert>
        <Scale className="h-4 w-4" />
        <AlertDescription>
          <strong>Legal Expertise Required:</strong> These items require assessment by qualified legal professionals 
          familiar with GDPR requirements. Automated analysis provides evidence, but human judgment is needed 
          for final compliance determination.
        </AlertDescription>
      </Alert>
    </div>
  );
}
